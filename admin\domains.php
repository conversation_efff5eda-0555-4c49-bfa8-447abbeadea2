<?php
/**
 * 域名管理页面 (优化版)
 */
// 包含头部
require_once 'common/header.php';

// 生成CSRF令牌
$csrfToken = generateCsrfToken();
?>

<!-- 操作按钮 -->
<?php 
echo renderTableButtons([
    [
        'id' => 'add-domain-btn',
        'text' => '添加域名',
        'icon' => 'layui-icon-add-1'
    ],
    [
        'id' => 'batch-delete-btn',
        'text' => '批量删除',
        'class' => 'layui-btn-danger',
        'icon' => 'layui-icon-delete'
    ]
]);
?>

<!-- 数据表格 -->
<table id="domain-table" lay-filter="domain-table"></table>

<!-- 表格操作按钮模板 -->
<script type="text/html" id="table-operation">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<!-- 状态切换模板 -->
<script type="text/html" id="status-switch">
    <input type="checkbox" name="status" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" lay-filter="status-switch" {{ d.status == 1 ? 'checked' : '' }}>
</script>

<!-- 授权类型模板 -->
<script type="text/html" id="auth-type">
    {{# if(d.is_trial == 1){ }}
    <span class="layui-badge">试用</span>
    {{# } else { }}
    <span class="layui-badge layui-bg-green">正式</span>
    {{# } }}
</script>

<!-- 域名添加/编辑表单 -->
<script type="text/html" id="domain-form">
    <form class="layui-form" lay-filter="domain-form" style="padding: 20px;">
        <input type="hidden" name="id" value="">
        <?php echo renderCsrfTokenField(); ?>
        
        <div class="layui-form-item">
            <label class="layui-form-label">域名</label>
            <div class="layui-input-block">
                <input type="text" name="domain" lay-verify="required|domain" autocomplete="off" placeholder="请输入域名" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="启用" checked>
                <input type="radio" name="status" value="0" title="禁用">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">授权类型</label>
            <div class="layui-input-block">
                <input type="radio" name="is_trial" value="0" title="正式版" checked>
                <input type="radio" name="is_trial" value="1" title="试用版">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">过期时间</label>
            <div class="layui-input-block">
                <input type="text" name="expire_time" id="expire-time" lay-verify="required" placeholder="请选择过期时间" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="domain-submit">提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</script>

<script>
layui.use(['admin', 'form', 'table', 'layer', 'laydate'], function(){
    var admin = layui.admin;
    var form = layui.form;
    var table = layui.table;
    var layer = layui.layer;
    var laydate = layui.laydate;
    var $ = layui.jquery;
    
    // 自定义验证规则
    form.verify({
        domain: [
            /^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/,
            '域名格式不正确'
        ]
    });
    
    // 渲染表格
    var domainTable = admin.renderTable({
        elem: '#domain-table',
        url: 'ajax.php?action=get_domains',
        cols: [[
            {type: 'checkbox'},
            {field: 'id', title: 'ID', width: 80, sort: true},
            {field: 'domain', title: '域名', width: 200},
            {field: 'auth_code', title: '授权码', width: 300},
            {field: 'status', title: '状态', width: 100, templet: '#status-switch'},
            {field: 'is_trial', title: '授权类型', width: 100, templet: '#auth-type'},
            {field: 'expire_time', title: '过期时间', width: 170, sort: true},
            {field: 'create_time', title: '创建时间', width: 170, sort: true},
            {fixed: 'right', title: '操作', toolbar: '#table-operation', width: 120}
        ]]
    });
    
    // 添加域名按钮点击事件
    $('#add-domain-btn').on('click', function(){
        // 打开添加表单
        openDomainForm();
    });
    
    // 批量删除按钮点击事件
    $('#batch-delete-btn').on('click', function(){
        admin.batchDelete({
            tableId: 'domain-table',
            url: 'ajax.php?action=batch_delete_domains',
            csrfToken: '<?php echo $csrfToken; ?>',
            successCallback: function(){
                domainTable.reload();
            }
        });
    });
    
    // 监听工具条
    table.on('tool(domain-table)', function(obj){
        var data = obj.data;
        
        if(obj.event === 'edit'){
            // 打开编辑表单
            openDomainForm(data);
        } else if(obj.event === 'del'){
            layer.confirm('确定删除该记录吗？', function(index){
                // 发送删除请求
                $.ajax({
                    url: 'ajax.php?action=delete_domain',
                    type: 'POST',
                    data: {
                        id: data.id,
                        csrf_token: '<?php echo $csrfToken; ?>'
                    },
                    dataType: 'json',
                    success: function(res){
                        if(res.code === 0){
                            layer.msg(res.msg, {icon: 1});
                            obj.del();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                
                layer.close(index);
            });
        }
    });
    
    // 监听状态切换
    form.on('switch(status-switch)', function(obj){
        var id = this.value;
        var status = obj.elem.checked ? 1 : 0;
        
        // 发送更新请求
        $.ajax({
            url: 'ajax.php?action=update_domain_status',
            type: 'POST',
            data: {
                id: id,
                status: status,
                csrf_token: '<?php echo $csrfToken; ?>'
            },
            dataType: 'json',
            success: function(res){
                if(res.code === 0){
                    layer.msg(res.msg, {icon: 1});
                } else {
                    layer.msg(res.msg, {icon: 2});
                    // 恢复原状态
                    $(obj.elem).prop('checked', !obj.elem.checked);
                    form.render('checkbox');
                }
            }
        });
    });
    
    // 处理域名表单提交
    admin.submitForm({
        formFilter: 'domain-form',
        submitFilter: 'domain-submit',
        url: function(data) {
            return data.field.id ? 'ajax.php?action=update_domain' : 'ajax.php?action=add_domain';
        },
        successCallback: function() {
            layer.closeAll('page');
            domainTable.reload();
        }
    });
    
    // 打开域名表单
    function openDomainForm(data){
        var title = data ? '编辑域名' : '添加域名';
        
        admin.openForm({
            title: title,
            content: $('#domain-form').html(),
            success: function(layero, index){
                // 渲染日期选择器
                admin.initDate('#expire-time', {
                    type: 'datetime',
                    format: 'yyyy-MM-dd HH:mm:ss',
                    min: 0
                });
                
                // 渲染表单
                form.render(null, 'domain-form');
                
                // 如果是编辑，填充表单数据
                if(data){
                    form.val('domain-form', {
                        'id': data.id,
                        'domain': data.domain,
                        'status': data.status,
                        'is_trial': data.is_trial,
                        'expire_time': data.expire_time
                    });
                } else {
                    // 默认过期时间为一年后
                    var defaultExpireTime = new Date();
                    defaultExpireTime.setFullYear(defaultExpireTime.getFullYear() + 1);

                    // 格式化为标准的 yyyy-MM-dd HH:mm:ss 格式
                    var year = defaultExpireTime.getFullYear();
                    var month = String(defaultExpireTime.getMonth() + 1).padStart(2, '0');
                    var day = String(defaultExpireTime.getDate()).padStart(2, '0');
                    var hours = String(defaultExpireTime.getHours()).padStart(2, '0');
                    var minutes = String(defaultExpireTime.getMinutes()).padStart(2, '0');
                    var seconds = String(defaultExpireTime.getSeconds()).padStart(2, '0');

                    var formattedTime = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
                    $('#expire-time').val(formattedTime);
                }
            }
        });
    }
});
</script>

<?php
// 包含底部
require_once 'common/footer.php';
?>