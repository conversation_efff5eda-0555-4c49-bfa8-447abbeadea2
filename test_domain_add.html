<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>域名添加测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>域名添加测试</h1>
        <p>此页面用于测试域名添加功能的修复效果</p>
        
        <form id="domainForm">
            <div class="form-group">
                <label for="domain">域名:</label>
                <input type="text" id="domain" name="domain" placeholder="例如: example.com" required>
            </div>
            
            <div class="form-group">
                <label for="status">状态:</label>
                <select id="status" name="status">
                    <option value="1">启用</option>
                    <option value="0">禁用</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="is_trial">授权类型:</label>
                <select id="is_trial" name="is_trial">
                    <option value="0">正式版</option>
                    <option value="1">试用版</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="expire_time">过期时间:</label>
                <input type="datetime-local" id="expire_time" name="expire_time" required>
            </div>
            
            <button type="submit">添加域名</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        // 设置默认过期时间为一年后
        function setDefaultExpireTime() {
            const now = new Date();
            now.setFullYear(now.getFullYear() + 1);
            
            // 格式化为 datetime-local 输入框需要的格式
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            
            const formattedTime = `${year}-${month}-${day}T${hours}:${minutes}`;
            document.getElementById('expire_time').value = formattedTime;
        }
        
        // 显示结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
        }
        
        // 页面加载时设置默认值
        document.addEventListener('DOMContentLoaded', function() {
            setDefaultExpireTime();
        });
        
        // 表单提交处理
        document.getElementById('domainForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            formData.append('action', 'add_domain');
            formData.append('domain', document.getElementById('domain').value);
            formData.append('status', document.getElementById('status').value);
            formData.append('is_trial', document.getElementById('is_trial').value);
            
            // 转换 datetime-local 格式为标准格式
            const expireTimeInput = document.getElementById('expire_time').value;
            const expireTime = expireTimeInput.replace('T', ' ') + ':00';
            formData.append('expire_time', expireTime);
            
            // 添加 CSRF token (这里使用模拟值，实际应用中需要从服务器获取)
            formData.append('csrf_token', 'test_token');
            
            try {
                showResult('正在发送请求...', 'info');
                
                const response = await fetch('/admin/ajax.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.text();
                
                try {
                    const jsonResult = JSON.parse(result);
                    const formatted = JSON.stringify(jsonResult, null, 2);
                    
                    if (jsonResult.code === 0) {
                        showResult(`✅ 添加成功\n\n${formatted}`, 'success');
                        // 清空表单
                        document.getElementById('domain').value = '';
                        setDefaultExpireTime();
                    } else {
                        showResult(`❌ 添加失败\n\n${formatted}`, 'error');
                    }
                } catch (e) {
                    showResult(`📄 原始响应\n\n${result}`, 'info');
                }
                
            } catch (error) {
                showResult(`🚫 网络错误\n\n${error.message}`, 'error');
            }
        });
    </script>
</body>
</html>
